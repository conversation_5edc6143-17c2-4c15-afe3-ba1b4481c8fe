import {commonRoutes} from '../utils/common-routes';
import { useCaps } from 'stores/caps';

export const PlanGenerator = (category: string) => [
    {
        path: '',
        name: 'plan-admin',
        meta: {category, sub: 'plan-admin', name: 'plan-dash'},
        component: () => import('src/components/plans/forms/PlanFunds.vue')
    },
    {
        path: 'enroll/:year?/:tab?',
        name: 'plan-gen-enroll',
        meta: {category, sub: 'plan-admin', name: 'plan-enroll'},
        component: () => import('src/components/plans/enrollments/pages/EnrollmentsAdmin.vue')
    },
    {
        path: 'edit',
        name: 'plan-gen-edit',
        meta: {category, sub: 'plan-admin', name: 'plan-edit'},
        component: () => import('src/components/plans/forms/PlanEdit.vue')
    },
    {
        path: 'payroll/:report?',
        name: 'plan-gen-payroll',
        meta: { category, sub: 'plan-payroll', name: 'plan-payroll' },
        component: () => import('src/components/plans/pages/PlanPayroll.vue')
    },
    {
        path: 'coverage',
        name: 'plan-gen-coverage',
        meta: {category, sub: 'plan-admin', name: 'plan-coverage'},
        component: () => import('src/components/coverages/lists/PlanCoverages.vue')
    },
    {
        path: 'funds',
        name: 'plan-gen-funds',
        meta: {category, sub: 'plan-admin', name: 'plan-funds'},
        component: () => import('src/components/plans/forms/PlanFunds.vue')
    },
    {
        path: 'permissions',
        name: 'plan-gen-permissions',
        meta: {category, sub: 'plan-admin', name: 'plan-permissions'},
        component: () => import('src/components/plans/forms/PlanPermissions.vue')
    },
    {
        path: 'team',
        name: 'plan-team',
        meta: {category, sub: 'plan-admin', name: 'plan-team'},
        component: () => import('src/components/plans/team/forms/PlanTeamForm.vue')
    },
    {
        path: 'care',
        meta: {category, sub: 'plan-admin', name: 'plan-care'},
        component: () => import('src/components/care/admin/pages/CareAdmin.vue'),
        children: [
            {
                path: '',
                name: 'plan-care',
                meta: {
                    subub: 'dash'
                },
                component: () => import('src/components/care/admin/pages/CareDash.vue'),
            },
            {
                path: 'events/:careId?',
                name: 'plan-cares',
                meta: {subub: 'events'},
                component: () => import('src/components/care/admin/pages/PlanCareEvents.vue')
            },
            {
                path: 'visits/:visitId?',
                name: 'plan-visits',
                meta: {subub: 'visits'},
                component: () => import('src/components/care/admin/pages/PlanVisits.vue')
            },
            {
                path: 'bills/:claimId?',
                name: 'plan-bills',
                meta: {subub: 'bills'},
                component: () => import('src/components/care/admin/pages/PlanBills.vue')
            }
        ].map(a => {
            return {
                ...a,
                meta: {
                    category,
                    sub: 'plan-admin',
                    name: 'plan-care',
                    ...a.meta
                }
            }
        })
    },
    {
        path: 'docs',
        name: 'plan-gen-docs',
        meta: {category, sub: 'plan-admin', name: 'plan-docs'},
        component: () => import('src/components/plans/docs/forms/PlanDocs.vue')
    }
]

export const adminRoutes = (orgId: string) => {
    return [
        {
            path: '/',
            component: () => import('src/layouts/PlanAdminLayout.vue'),
            children: [
                ...commonRoutes(),
                {
                    path: '/plan-docs/:planId?',
                    name: 'plan-docs',
                    component: () => import('src/components/plans/docs/pages/PlanDocs.vue')
                },
                {
                    path: '/plan-print/:tab?',
                    name: 'doc-print',
                    component: () => import('src/components/plans/docs/pages/DocPrint.vue')
                },
                ...[
                    {
                        path: '',
                        name: 'org-dash',
                        component: () => import('src/components/orgs/dashboard/pages/OrgDashboard.vue'),
                        meta: {
                            category: ''
                        }
                    },
                    {
                        path: '/comp/:tab?',
                        name: 'org-comp',
                        component: () => import('src/components/comps/pages/OrgComps.vue'),
                        meta: {
                            category: 'org-comp'
                        }
                    },
                    {
                        path: '/plans',
                        name: 'group-plans-admin',
                        meta: {
                            category: 'plans'
                        },
                        component: () => import('src/components/plans/admin/pages/PlansAdmin.vue'),
                        props: {
                            selectMode: true
                        }
                    },
                    {
                        path: '/plan',
                        component: () => import('src/components/plans/forms/PlanGenerator.vue'),
                        meta: {
                            category: 'plans'
                        },
                        children: PlanGenerator('plans')
                    },
                    {
                        path: '/coverages',
                        name: 'org-coverages',
                        component: () => import('src/components/coverages/pages/CoverageAdmin.vue'),
                    },
                    {
                        path: '/coverage/:coverageId',
                        name: 'coverage-admin',
                        meta: {
                            category: 'plans'
                        },
                        props: {
                            admin: true
                        },
                        component: () => import('src/components/coverages/pages/CoveragePage.vue')
                    },
                    {
                        path: '/members/:groupId?',
                        name: 'org-members',
                        meta: {
                            category: 'people',
                            name: 'groups'
                        },
                        component: () => import('src/components/groups/members/GroupMembers.vue')
                    },
                    {
                        path: '/permissions',
                        name: 'org-caps',
                        meta: {
                            category: 'people',
                            name: 'caps'
                        },
                        component: () => import('src/components/capabilities/orgs/pages/OrgCaps.vue')
                    },
                    {
                        path: '/care/:careId?',
                        name: 'org-claims',
                        component: () => import('src/components/care/plan-admin/pages/OrgCare.vue')
                    },
                    {
                        path: '/settings',
                        meta: {
                            category: 'settings',
                            ucan: {
                                requiredCapabilities: [[`orgs:${orgId}`, ['orgAdmin']], [`orgs:${orgId}`, ['WRITE']], ['orgs', 'WRITE']],
                                or: true,
                                cap_subjects: [orgId],
                                capStore: useCaps()
                            }
                        },
                        props: {
                            modelValue: orgId
                        },
                        component: () => import('src/components/orgs/pages/OrgDetails.vue'),
                        children: [
                            {
                                path: '',
                                name: 'org-settings',
                                meta: {name: 'org-info'},
                                component: () => import('src/components/orgs/forms/OrgInfo.vue'),
                                props: {
                                    modelValue: orgId
                                }
                            },
                            {
                                path: '/control',
                                name: 'org-control',
                                meta: {name: 'org-control'},

                                component: () => import('src/components/orgs/control/OrgControl.vue'),
                                props: {
                                    modelValue: orgId
                                }
                            },
                            {
                                path: '/ownership',
                                name: 'org-ownership',
                                meta: {name: 'org-ownership'},

                                component: () => import('src/components/orgs/owners/OrgOwnership.vue'),
                                props: {
                                    modelValue: orgId
                                }
                            },
                            {
                                path: '/info',
                                name: 'org-info',
                                meta: {name: 'org-info'},

                                component: () => import('src/components/orgs/forms/OrgInfo.vue'),
                                props: {
                                    modelValue: orgId
                                }
                            },
                            {
                                path: '/banking',
                                meta: {name: 'org-banking'},
                                component: () => import('src/components/accounts/pages/BankingSettings.vue'),
                                children: [
                                    {
                                        path: '',
                                        name: 'org-banking',
                                        meta: {
                                            sub: 'bank-accounts'
                                        },
                                        component: () => import('src/components/accounts/forms/OrgAccounts.vue')
                                    },
                                    {
                                        path: 'profile',
                                        name: 'banking-profile',
                                        meta: {sub: 'banking-profile'},
                                        component: () => import('src/components/accounts/treasury/pages/ConnectAccount.vue')
                                    }
                                ].map(a => {
                                    return {
                                        ...a,
                                        meta: {category: 'settings', name: 'org-banking', ...a.meta}
                                    }
                                })
                            }
                        ]
                    },
                    {
                        path: '/plan-wallet',
                        props: {
                            modelValue: orgId
                        },
                        component: () => import('src/components/accounts/pages/OrgBanking.vue'),
                        children: [
                            {
                                path: '',
                                meta: {
                                    category: 'finance',
                                    name: 'care-account-dash'
                                },
                                component: () => import('src/components/care-accounts/pages/CareAccounts.vue'),
                                children: [
                                    {
                                        path: '',
                                        name: 'care-account-dash',
                                        meta: {sub: 'dash'},
                                        component: () => import('src/components/care-accounts/pages/CareAccount.vue')
                                    },
                                    {
                                        path: 'account-settings',
                                        name: 'care-account-settings',
                                        meta: {sub: 'settings'},
                                        component: () => import('src/components/care-accounts/pages/CareAccountSettings.vue')
                                    },
                                    {
                                        path: 'budgets',
                                        name: 'org-budgets',
                                        meta: {sub: 'budgets'},
                                        component: () => import('src/components/accounts/issuing/components/IssuingHome.vue')
                                    },
                                    {
                                        path: 'plan-budgets',
                                        name: 'plan-budgets',
                                        meta: {sub: 'plan-budgets'},
                                        component: () => import('src/components/accounts/issuing/components/budgets/lists/PlanSpendByBudget.vue')
                                    },
                                    {
                                        path: 'budgets/:budgetId/:tab?',
                                        meta: {sub: 'budgets'},
                                        name: 'org-budget',
                                        component: () => import('src/components/accounts/issuing/components/budgets/pages/BudgetPage.vue')
                                    },
                                    {
                                        path: 'limit/:limitId',
                                        meta: {sub: 'budgets'},
                                        name: 'limit-page',
                                        component: () => import('src/components/accounts/issuing/components/limits/pages/LimitPage.vue')
                                    }
                                ].map(a => {
                                    return {
                                        ...a,
                                        meta: {
                                            category: 'finance',
                                            name: 'care-account',
                                            ...a.meta
                                        }
                                    }
                                })
                            }

                        ]
                    }
                ].map((a: any) => {
                    return {
                        ...a,
                        meta: {
                            ...a.meta,
                            ucan: {
                                requiredCapabilities: [['orgs', 'WRITE'], [`orgs:${orgId}`, ['planAdmin']], [`orgs:${orgId}`, ['orgAdmin']], [`orgs:${orgId}`, ['WRITE']]],
                                or: true,
                                cap_subjects: [orgId],
                                capStore: useCaps()
                            }
                        }
                    }
                })
            ]
        }

    ]
}
