<template>
  <div class="_fw">
    <template v-if="!isActive && plan">
      <div class="font-1r tw-six">Activate Enrollment {{ name.y }} - {{ name.v }}?</div>
      <div class="font-1r">This will invite <span class="num-font text-primary tw-six">{{ eligibleCount }}</span>
        people to enroll in {{ plan.name }}
      </div>

      <div class="q-pt-md row items-center justify-end">
        <template v-if="!activating">
          <q-btn flat icon-right="mdi-close" label="Cancel" no-caps @click="emit('cancel')"></q-btn>
          <q-btn push class="_p_btn" label="Activate" icon-right="mdi-check" no-caps
                 @click="activateEnrollment"></q-btn>
        </template>
        <template v-else>
          <div class="font-1r text-italic q-pr-sm">Activating</div>
          <q-spinner size="25px" color="primary"></q-spinner>
        </template>
      </div>
    </template>
    <template v-else>
      <div class="font-1r tw-six">Plan Enrollment {{ name.y }} - {{ name.v }}</div>
      <div class="font-1r">
        <span><span class="text-primary tw-six">{{enrolled}}</span> of <span class="text-primary tw-six">{{eligibleCount || 'total eligible'}}</span> enrolled</span>
      </div>
      <div class="q-pt-sm _fw font-1r">If you've added people or made changes, you can re-sync to make sure everyone has a valid enrollment open.</div>
      <div class="q-pt-md _fw">
        <q-btn size="sm" no-caps class="tw-six" color="primary" icon-right="mdi-refresh" label="Sync Now"></q-btn>
      </div>
    </template>
  </div>
</template>

<script setup>
  import {computed, ref} from 'vue';
  import {$errNotify} from 'src/utils/global-methods';
  import {usePlans} from 'stores/plans';

  const planStore = usePlans();

  const emit = defineEmits(['done', 'cancel'])
  const props = defineProps({
    plan: { required: true },
    planKey: { required: true },
    eligibleCount: Number,
  })

  const name = computed(() => {
    const obj = { y: '', v: '' };
    const spl = (props.planKey || '_').split('_');
    obj.y = spl[0] || '';
    obj.v = spl[1] || '';
    return obj;
  })

  const isActive = computed(() => {
    return props.plan?.enrollments[props.planKey]?.active
  })
  const enrolled = computed(() => {
    return props.plan?.enrollments[props.planKey]?.enrolled || 0
  })

  const activating = ref(false);
  const activateEnrollment = async () => {
    activating.value = true;
    const newPlan = await planStore.patch(props.plan._id, { _activate: props.planKey })
        .catch(err => {
          console.error(err);
          $errNotify('Error activating - see status update');
          activating.value = false;
        })
    activating.value = false;
    if (newPlan?._id) emit('done')
  }
</script>

<style lang="scss" scoped>

</style>
