<template>
  <div class="_fw">
    <div class="_fw _frm _f_g_r">
      <div class="_lbl">
        Enrollment
      </div>
      <div class="_bod flex items-center font-1r q-pa-sm">
        <div>
          <span class="alt-font font-7-8r">Year: <b>{{ name.y }}</b> - Version: <b>{{ name.v }}</b></span>
        </div>
      </div>
      <div class="_lbl">
        Type
      </div>
      <div class="_bod q-pa-sm">
        <q-chip v-if="enrollment?.open_enroll" color="p1" class="tw-six">Open</q-chip>
        <q-chip v-else color="s1" class="tw-six">Special</q-chip>
      </div>
      <div class="_lbl">Description</div>
      <div class="_bod q-pa-sm">
        <div class="font-7-8r">{{
            enrollment?.description || (enrollment?.open_enroll ? `${name.y} Open Enrollment` : `${name.y} Special Enrollment`)
          }}
        </div>
      </div>
      <div class="_lbl">From</div>
      <div class="_bod q-pa-sm">
        <q-chip color="white">
          <q-icon name="mdi-calendar" color="p7" class="q-mr-xs"></q-icon>
          <span>{{ formatDate(enrollment?.open, 'MMMM DD, YYYY') }}</span>
        </q-chip>
      </div>
      <div class="_lbl">To</div>

      <div class="_bod q-pa-sm">
        <q-chip color="white">
          <q-icon name="mdi-calendar" color="s7" class="q-mr-xs"></q-icon>
          <span>{{ formatDate(enrollment?.close, 'MMMM DD, YYYY') }}</span>
        </q-chip>
      </div>

      <div class="_lbl">Enrollment</div>

      <div class="_bod q-pa-sm">
        <div v-if="enrollment?.active" class="font-1r num-font q-pb-sm"><span><b>{{ enrolledTotal }}</b> of
          <b>{{ eligibleCount }}</b> eligible persons have
          enrolled | <q-chip dense color="transparent" clickable @click="activateDialog = true">
            <span class="q-mr-sm">Re-sync</span>
            <q-icon name="mdi-refresh" color="primary"></q-icon>
          </q-chip>
          </span>
        </div>
        <div v-else class="font-1r q-px-md">
          Enrollment not activated - <span class="tw-six text-primary cursor-pointer" @click="activateDialog = true">activate now</span>
        </div>
        <div class="row items-center">
          <div class="col-12 q-pa-sm" v-if="!enrollment?.open_enroll">
            <q-btn-group rounded glossy>
              <q-btn :class="tab === 'open_to' ? '__on' : '__off'" no-caps @click="tab='open_to'"
                     label="Eligible"></q-btn>
              <q-btn :class="tab === 'enrolled' ? '__on' : '__off'" no-caps @click="tab='enrolled'"
                     label="Enrolled"></q-btn>
            </q-btn-group>
          </div>
        </div>


        <q-tab-panels animated class="_panel" v-model="tab">
          <q-tab-panel class="_panel" name="open_to" v-if="!enrollment?.open_enroll">
            <div class="col-12 q-pa-sm">
              <q-input filled v-model="search.text" placeholder="Search...">
                <template v-slot:prepend>
                  <q-icon name="mdi-magnify"></q-icon>
                </template>
              </q-input>
            </div>
            <q-table
                class="q-mt-md"
                dense
                id="I_S"
                flat
                :rows="g$.data?.map(a => getGroup(a))"
                :columns="gCols"
                hide-pagination
                row-key="_id"
            >
              <template v-slot:no-data>
                <div class="q-pa-sm text-italic">No groups added</div>
              </template>
              <template v-slot:top>
                <div class="flex items-center q-px-sm">
                  <div class="font-7-8r tw-six">Eligible Groups</div>
                </div>
              </template>
              <template v-slot:bottom>
                <div class="row _fw">
                  <div class="font-7-8r text-grey-8">{{ whiteListGroups?.length ? 1 : 0 }} - {{
                      whiteListGroups?.length || 0
                    }} of
                    {{ whiteListGroups.length }}
                  </div>
                </div>
              </template>
            </q-table>

            <q-separator color="white" class="q-my-sm"></q-separator>

            <q-table
                dense
                id="I_S"
                flat
                :rows="p$.data"
                :columns="pCols"
                virtual-scroll
                @virtual-scroll="senseScrollLoad"
                hide-pagination
                row-key="_id"
            >
              <template v-slot:no-data>
                <div class="q-pa-sm text-italic">No individuals added</div>
              </template>
              <template v-slot:top>
                <div class="flex items-center q-px-sm">
                  <div class="font-7-8r tw-six">Eligible People</div>
                </div>
              </template>
              <template v-slot:bottom>
                <div class="row _fw">
                  <div class="font-7-8r text-grey-8">{{ p$.data?.length ? 1 : 0 }} - {{ p$.data?.length || 0 }} of
                    {{ p$.total }}
                  </div>
                </div>
              </template>
            </q-table>
          </q-tab-panel>
          <q-tab-panel class="_panel" name="enrolled">
            <plan-enrollment-table
                :plan="fullPlan"
                :plan-key="planKey"
            ></plan-enrollment-table>
          </q-tab-panel>
        </q-tab-panels>
      </div>

    </div>

    <common-dialog setting="small" v-model="activateDialog">
      <div class="_fw q-pa-lg br10 bg-white bs2-8">
        <activate-card
            :eligible-count="eligibleCount"
            :plan="fullPlan"
            :plan-key="planKey"
            @cancel="activateDialog = false"
            @done="activateDialog = false"
        ></activate-card>

      </div>
    </common-dialog>

  </div>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import ActivateCard from 'components/plans/enrollments/cards/ActivateCard.vue';
  import PlanEnrollmentTable from 'components/plans/enrollments/cards/PlanEnrollmentTable.vue';


  import {computed, ref, watch} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {_get} from 'symbol-syntax-utils';
  import {HFind, hInfiniteScroll} from 'src/utils/hFind';
  import {$capitalizeFirstLetter} from 'src/utils/global-methods';

  import {usePlans} from 'stores/plans';
  import {usePpls} from 'stores/ppls';
  import {useGroups} from 'stores/groups';
  import {formatDate} from 'src/utils/date-utils';
  import {HQuery} from 'src/utils/hQuery';
  import {useEnrollments} from 'stores/enrollments';


  const planStore = usePlans();
  const pplsStore = usePpls();
  const groupStore = useGroups();
  const eStore = useEnrollments();

  const props = defineProps({
    planKey: String,
    plan: Object
  })

  const activateDialog = ref(false);

  const { item: fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan)
  })
  const enrollment = computed(() => {
    return _get(fullPlan.value, ['enrollments', props.planKey])
  })

  const tab = ref('open_to')

  const name = computed(() => {
    const obj = { y: '', v: '' };
    const spl = (props.planKey || '_').split('_');
    obj.y = spl[0] || '';
    obj.v = spl[1] || '';
    return obj;
  })

  const whiteListPpl = computed(() => _get(fullPlan.value, ['enrollments', props.planKey, 'ppls']) || [])
  const whiteListGroups = computed(() => {
    return fullPlan.value.groups || [];
  })

  const { search, searchQ } = HQuery({})

  const { h$: p$ } = HFind({
    store: pplsStore,
    limit: ref(10),
    pause: computed(() => !whiteListPpl.value?.length),
    params: computed(() => {
      return {
        query: { _id: { $in: whiteListPpl.value } },
        ...searchQ.value
      }
    })
  })

  const { h$: g$ } = HFind({
    store: groupStore,
    limit: ref(25),
    pause: computed(() => !whiteListGroups.value?.length),
    params: computed(() => {
      return {
        query: {
          _id: { $in: whiteListGroups.value }
        }
      }
    })
  })

  const gCols = computed(() => {
    return [
      {
        label: 'Organization',
        name: 'org',
        field: 'orgName'
      },
      {
        label: 'Group',
        name: 'name',
        field: 'name'
      }
    ].map(a => {
      return {
        sortable: true,
        align: 'left',
        ...a
      };
    })
  })

  const enrolledTotal = ref(0);
  watch(whiteListGroups, async (nv, ov) => {
    if (nv && nv.length !== ov?.length) {
      const res = await eStore.find({
        query: {
          plan: fullPlan.value._id,
          version: props.planKey,
          status: 'complete',
          $limit: 0
        }
      })
      enrolledTotal.value = res?.total || 0;
    }
  }, { immediate: true })

  const getGroup = (grp) => {
    const orgName = grp.key?.split(':')[0].split('_').map(a => $capitalizeFirstLetter(a)).join(' ');
    return {
      ...grp,
      orgName
    }
  }

  const { senseScrollLoad } = hInfiniteScroll({ h$: p$, loadNum: 10 })


  const eligibleCount = computed(() => {
    let g = 0;
    for (const grp of g$.data || []) {
      g += grp.members?.length || 0
    }
    return g + whiteListPpl.value?.length || 0
  })

</script>

<style lang="scss" scoped>
  .__off {
    font-weight: 600;
    background: white;
    color: var(--q-p7);
  }

  .__on {
    font-weight: 600;
    color: white;
    background: var(--q-p7);
  }
  ._bod {
    padding: 5px 10px;
  }

</style>
