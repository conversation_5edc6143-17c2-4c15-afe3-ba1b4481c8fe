<template>
  <div class="_fw">
    <div class="flex items-center">
      <template v-for="(item, i) in ['changes', 'enrollment', 'eligible', 'events']" :key="item">
        <q-chip square dense color="transparent" clickable @click="setTab(item)" :label="$capitalizeFirstLetter(item)"
                :class="`text-ir-deep ${tab === item ? 'tw-six' : ''}`"></q-chip>
        <div v-if="i < 3" class="text-ir-mid">|</div>
      </template>
    </div>

    <q-tab-panels class="_panel" animated v-model="tab">
      <q-tab-panel class="_panel" name="changes">
        <enrollment-changes :plan="fullPlan" :plan-year="yr"></enrollment-changes>
      </q-tab-panel>

      <q-tab-panel class="_panel" name="enrollment">
        <individual-enrollments :plan="plan"></individual-enrollments>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="eligible">
        <eligibility-check :plan="fullPlan"></eligibility-check>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="events">

        <!--          EVENTS TABLE-->
        <q-tab-panels animated class="_panel" :model-value="!!selected" transition-next="jump-up" transition-prev="jump-down">
          <q-tab-panel class="_panel" :name="false">
            <q-table
                class="bg-transparent"
                flat
                hide-pagination
                :columns="cols"
                :rows="Object.keys(enrollment || {}).map(a => getRow(a))"
            >

              <template v-slot:no-data>
                <div class="q-pa-sm text-italic font-7-8r">
                  No enrollments created yet
                </div>
              </template>
              <template v-slot:header="scope">
                <q-th
                    v-for="col in scope.cols"
                    :key="col.name"
                    :props="scope"
                >
                  {{ col.label }}
                </q-th>
              </template>
              <template v-slot:body="scope">
                <q-tr :props="scope" @click="selected = `${String(yr)}_${String(scope.row.subK)}`">
<!--                  <q-td>-->
<!--                    <q-btn dense flat size="sm"-->
<!--                           icon="mdi-dots-horizontal">-->
<!--                    </q-btn>-->
<!--                  </q-td>-->
                  <q-td v-for="(col, i) in scope.cols" :key="`td-${i}`">
                    <component :is="col.component" v-bind="col.attrs(scope.row)"></component>
                  </q-td>
                </q-tr>
              </template>
            </q-table>
          </q-tab-panel>
          <q-tab-panel class="_panel" :name="true">
            <plan-enrollments @back="selected = ''" :plan="fullPlan" :plan-key="selected"></plan-enrollments>
          </q-tab-panel>
        </q-tab-panels>

      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup>
  import EnrollmentChanges from 'components/plans/enrollments/pages/EnrollmentChanges.vue';
  import EligibilityCheck from 'components/plans/enrollments/cards/EligibilityCheck.vue';
  import PlanEnrollments from 'components/plans/enrollments/pages/PlanEnrollments.vue';
  import IndividualEnrollments from 'components/plans/enrollments/pages/IndividualEnrollments.vue';
  import TdChip from 'components/common/tables/TdChip.vue';

  import {computed, watch, ref} from 'vue';
  import {formatDate, isBetweenDates} from 'src/utils/date-utils';
  import {$capitalizeFirstLetter, $limitStr, dollarString} from 'src/utils/global-methods';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {getCurrentPlanYear} from 'components/plans/utils';
  import {enrollmentsByYear} from 'components/plans/enrollments/utils';
  import {useRoute, useRouter} from 'vue-router';

  const planStore = usePlans();
  const route = useRoute();
  const router = useRouter();

  const props = defineProps({
    plan: { required: true },
    year: { required: false }
  })

  const tab = ref('changes');
  const routeTab = computed(() => route.params.tab);
  watch( routeTab, (nv) => {
    if (nv && nv !== tab.value) tab.value = nv
  }, { immediate: true });

  const setTab = (t) => {
    tab.value = t;
    router.push({ ...route, params: { year: props.year || new Date().getFullYear(), tab: t } })
  }

  const { item: fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan)
  })

  const yr = computed(() => String(props.year || getCurrentPlanYear(fullPlan.value)));

  const enrollment = computed(() => {
    if (!fullPlan.value?.enrollments) return {}
    const e = enrollmentsByYear(fullPlan.value);
    if (!e) return {};
    return e[yr.value];
  })


  const isOpen = computed(() => {
    const obj = {};
    const time = new Date().getTime();
    for (const k in fullPlan.value?.enrollments || {}) {
      const { open, close } = fullPlan.value.enrollments[k] || { close: undefined }
      obj[k] = { open: false, close: false }
      obj[k].open = isBetweenDates(new Date(), open, close)
      obj[k].close = open && (!close || new Date(close).getTime() <= time)
    }
    return obj;
  })

  const selected = ref('');


  const getRow = (subK) => {
    return {
      ...enrollment.value[subK],
      subK
    }
  }

  const cols = computed(() => {
    return [
      {
        name: 'Event',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              color: 'white',
              class: 'text-p9 tw-six',
              label: `${yr.value} - ${row.subK}`
            }
          }
        }
      },
      {
        name: 'Open',
        component: TdChip,
        attrs: (row) => {
          const { open, close } = isOpen.value[`${selected.value}`] || {};
          return {
            chipAttrs: {
              class: 'tw-six',
              label: formatDate(row.open, 'MM/DD/YYYY'),
              color: open ? 'green-7' : close ? 'grey-6' : 'blue',
              outline: true,
            }
          }
        }
      },
      {
        name: 'Close',
        component: TdChip,
        attrs: (row) => {
          const { open, close } = isOpen.value[`${selected.value}`] || {};
          return {
            chipAttrs: {
              class: 'tw-six',
              label: formatDate(row.close, 'MM/DD/YYYY'),
              color: open ? 'green-10' : close ? 'grey-6' : 'blue-10',
              outline: true,
            }
          }
        }
      },
      {
        name: 'Description',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              label: $limitStr(row.description, 35, '...'),
              color: 'white'
            }
          }
        }
      },
      {
        name: 'Enrolled',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              label: row.active ? dollarString(row.enrolled, '', 0) : 'Not Active',
              color: row.active ? 'white' : 'red',
              outline: !row.active
            }
          }
        }
      }
    ].map(a => {
      return {
        ...a,
        label: a.name,
        sortable: true,
        align: 'left',
        field: a.name
      };
    });
  })

</script>

<style lang="scss" scoped>

</style>
