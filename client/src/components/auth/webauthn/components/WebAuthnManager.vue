<template>
  <div class="_fw">
    <template v-if="!error">
    <div class="font-1r tw-five text-primary q-px-sm">Device passkey login (Face ID, Touch ID, password keychain, etc)</div>
    <q-list separator>
      <q-item-label header>This device has passkeys for the following domains:</q-item-label>
      <q-item clickable v-if="!p$.isPending &&!thisSite.total" @click="addPasskey">
        <q-item-section>
          <q-item-label header class="text-accent">Add passkey for {{ rpID }}</q-item-label>
        </q-item-section>
      </q-item>
      <q-item v-for="(pk, i) in p$.data" :key="`pk-${i}`">
        <q-item-section>
          <q-item-label>{{ pk.rpID }}</q-item-label>
          <q-item-label caption>{{ pk.name || 'Unnamed' }}</q-item-label>
        </q-item-section>
      </q-item>
    </q-list>
    </template>
    <q-slide-transition>
    <div v-if="error" class="_fw">
      <div class="q-pa-md font-1r text-italic text-red">{{error}}</div>
    </div>
    </q-slide-transition>

  </div>
</template>

<script setup>

  import {loginPerson} from 'stores/utils/login';
  import {HFind} from 'src/utils/hFind';
  import {computed, ref} from 'vue';
  import {usePasskeys} from 'stores/passkeys';
  import {getRpID, registerPasskey} from 'components/auth/webauthn/webauthn';
  import {$successNotify} from 'src/utils/global-methods';

  const keyStore = usePasskeys()

  const { login } = loginPerson();

  const rpID = computed(() => getRpID());
  const error = ref('');

  const thisSite = computed(() => keyStore.findInStore({ query: { rpID: rpID.value } }))

  const addPasskey = async () => {
    try {
      const res = await registerPasskey({ login: login.value })
          .catch(err => {
            console.error(`Error retrying passkey: ${err.message}`)
            error.value = `Failed to register passkey: ${err.message}`;
            return {}
          })
      if (res.success) $successNotify('Passkey added!');
      else {
        if (!error.value) error.value = res.message;
        return;
      }
    } catch (e){
      console.error(`Error registering passkey: ${e.message}`)
      error.value = 'Failed to register passkey';
    }
  }

  const { h$: p$ } = HFind({
    store: keyStore,
    limit: ref(25),
    params: computed(() => {
      return {
        query: {
          login: login.value?._id
        }
      }
    })
  })
</script>

<style lang="scss" scoped>

</style>
