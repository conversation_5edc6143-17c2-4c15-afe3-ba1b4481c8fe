<template>
  <div class="__res_cat">

    <div class="row justify-center bg-ir-grad" id="RC1" v-if="visible[1]">
      <div class="_cent pw2 pd10">
        <div class="_fw row justify-center q-pb-lg">
          <div class="_xsent pw4">
            <div class="__head">Top results for you</div>
            <div class="__title">
              Based on the data in your profile and our simulated outcomes, these are the plans we found most likely to
              be your most affordable options.
            </div>

            <div class="q-py-sm tw-five text-xs text-ir-mid text-center">Over 200 products analysed - reduced to the top
              {{ byId.average?.length || 20 }} to optimize memory
            </div>

          </div>
        </div>

        <template v-if="byId.average?.length">
          <div class="row">
            <div class="col-12 col-md-4 q-pa-xs" v-for="(p, i) in (byId.average || []).slice(0, 3)" :key="`top-${i}`">
              <div class="__top3 cursor-pointer" @click="emit('update:selected', byId.all[p])">
                <div v-if="byId.all[p]" class="_fw"
                     :style="{borderBottom: `solid 3px var(--q-${productTypes[pt(byId.all[p])].color})`}">
                  <spend-card
                      :tax_rate="tax_rate"
                      :def_age="shop.stats.age"
                      :def_key="def_key"
                      :useAptc="shop.useAptc"
                      :mult="mult"
                      :model-value="byId.all[p]"
                      :scores="(shop.coverage_scores || {})[p]"
                      :dark="dark"
                      :aptc="shop.aptc"
                  ></spend-card>
                </div>
              </div>
            </div>
          </div>

          <div class="_fw row justify-center __ai_chip" v-if="shop._id">
            <div :class="`AiChip ${chatOn ? '__on' : ''}`">
              <shop-ai-chip
                  v-model="chatOn"
                  dark
                  :prompts="['Which option is the most similar to my current plan?', 'How does my plan compare to the other options?', 'How does the premium tax credit affect things?', 'When would I want to choose a health share?']"
                  color="a10"
                  class="tw-five text-xs alt-font text-white"
                  :shop="shop"
              ></shop-ai-chip>
            </div>
          </div>
        </template>
        <div v-else class="row">
          <div class="col-12 col-md-4 q-pa-xs" v-for="i in 3" :key="`skd-${i}`">
            <q-skeleton class="_fw h200"></q-skeleton>
          </div>
        </div>
      </div>
    </div>


    <div class="_fw pd10" id="RC2">
      <template v-if="visible[2]">
        <div class="_fw row justify-center q-pb-lg">
          <div class="_xsent pw4">
            <div class="__head">Find the right price</div>
            <div class="__title">
              The first thing to understand is your ideal premium/deductible. Insurance is for the "what-if" but the
              cost
              of
              premiums is 100% guaranteed.
            </div>

          </div>
        </div>
        <div class="_fw row justify-center">
          <div class="_cent pw2">
            <ideal-structure
                :tax_rate="tax_rate"
                :shop="shop"
                :enrollment="enrollment"
                :def_key="def_key"
                @update:selected="emit('update:selected', $event)"
                :all="all"
            ></ideal-structure>
          </div>
        </div>
      </template>
    </div>
    <div class="_fw pd10 bg-ir-bg1" id="RC3">
      <template v-if="visible[3]">
        <div class="_fw row justify-center q-pb-lg">
          <div class="_sent pw2">
            <div class="__head">Ranking By Spend</div>
            <div class="__title">
              <div>See how each plan performs if you have
                <q-chip dark dense class="tw-six" color="accent" square>this</q-chip>
                many medical bills/yr - <span class="alt-font tw-six text-a3">total spend</span>
                (Premium + OOP)
              </div>
            </div>
          </div>
        </div>
        <div class="_fw row justify-center">
          <div class="_cent pw2">

            <spend-distribution
                :tax_rate="tax_rate"

                :shop="shop"
                @update:selected="emit('update:selected', $event)"
                :lists="lists"
                :all="all"
                :ranks="ranks"
                :dist="dist"
            ></spend-distribution>
          </div>
        </div>
      </template>
    </div>
    <div class="_fw pd10" id="RC4">
      <template v-if="visible[4]">
        <div class="_fw row justify-center">
          <div class="_sent pw2">

            <div class="__head">Ranking By Average</div>
            <div class="__title">
              <div>'Best average' means most likely to work out overall</div>
              <div>
                <span class="text-xxs tw-six">(with {{
                    dollarString(shop.spend, '$', 0)
                  }} in average annual bills)</span>
              </div>
            </div>
            <div class="__list _fw">

              <div class="__pil">
                <div v-for="(h, i) in cols" :key="`h-${i}`">
                  {{ h.label }}
                  <q-icon color="primary" name="mdi-information"></q-icon>
                  <q-tooltip class="text-xxs tw-six">{{ h.description }}</q-tooltip>
                </div>
              </div>

              <div class="_fw" v-if="byId.average?.length">
                <div v-if="!list.length" class="q-pa-md text-italic">No Results</div>
                <div v-for="(k, i) in list" :key="k" class="__policy_row" @click="emit('update:selected', byId.all[k])">
                  <policy-row
                      :tax_rate="tax_rate"

                      :use-aptc="shop.useAptc"
                      :sims-count="shop.simsCount"
                      :mult="mult"
                      dark
                      :rank="i"
                      :score="byId.scores[k]"
                      :model-value="byId.all[k]"
                  ></policy-row>
                </div>
              </div>
              <div v-else>
                <q-list>
                  <q-item v-for="i in 3" :key="`sk-${i}`">
                    <q-item-section avatar>
                      <q-skeleton :dark="dark" type="circle" class="__bow"></q-skeleton>
                    </q-item-section>
                    <q-item-section>
                      <q-skeleton :dark="dark" height="40px"></q-skeleton>
                    </q-item-section>
                  </q-item>
                </q-list>

              </div>
            </div>

            <div class="row justify-center q-py-md" v-if="byId[showKey]?.length > showLimit">
              <q-chip size="sm" :dark="dark" :color="`ir-grey-${dark ? '10' : '2'}`" class="tw-six" clickable
                      @click="showLimit += 10"
                      label="Show More"></q-chip>
            </div>
          </div>
        </div>
      </template>
    </div>
    <div class="_fw pd10 bg-ir-grad" id="RC5">
      <template v-if="visible[5]">
        <div class="_fw row justify-center">
          <div class="_sent pw2">
            <div class="__head q-py-md">Spend Distribution</div>
            <div class="__title">
              <div>The 'how' bills happen is almost as important as 'how much'</div>
              <div>coverage is useless until suddenly it's critical</div>
            </div>
            <spend-graph :all-spend="shop.allSpend" :totals="shop.spend_dist || {}"></spend-graph>

            <div :class="`q-pa-md text-${dark ? 'ir-grey-3' : 'ir-grey-8'}`">
              <div class="q-pt-md text-xs tw-six">What does this data mean for you?</div>
              <div class="text-xxs tw-five q-py-sm">Most of the cost comes in your worst years. Most bills by volume are
                small
                and you're paying 100% out of pocket - or you won't make up the extra money you spent on premium for a
                lower
                deductible.
                <div class="q-pt-sm">
                  On very large bills (where most of the total cost comes from), you reach your legal max out of pocket
                  (moop). That's why you allow insurance networks to dictate your care choices as little as possible -
                  insurance
                  is a backstop, not a tour guide.
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>

    <div class="_fw pd10 pw2" id="RC6">
      <template v-if="visible[6]">
        <div class="row justify-center">
          <div class="_sent">
            <what-if :shop="shop"></what-if>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
  import PolicyRow from 'components/market/shop/cards/PolicyRow.vue';
  import SpendGraph from 'components/market/shop/cards/SpendGraph.vue';
  import SpendDistribution from 'components/market/shop/cards/SpendDistribution.vue';
  import IdealStructure from 'components/market/shop/cards/IdealStructure.vue';
  import SpendCard from 'components/market/shop/cards/SpendCard.vue';
  import WhatIf from 'components/market/shop/cards/WhatIf.vue';

  import {computed, onMounted, ref} from 'vue';
  import {dollarString} from 'symbol-syntax-utils';
  import {darkness} from 'src/utils/env/darkness';
  import {pt, productTypes} from 'components/market/utils';
  import ShopAiChip from 'components/market/shop/ai/ShopAiChip.vue';

  const emit = defineEmits(['update:selected']);
  const props = defineProps({
    def_key: { required: true },
    shop: {
      default: () => {
        return { stats: {} }
      }
    },
    tax_rate: Number,
    enrollment: { required: false },
    activeTypes: { required: true, type: Object },
    showKey: { default: 'average' },
    byId: {
      default: () => {
        return {}
      }
    }
  })

  const { dark } = darkness()
  const mult = computed(() => props.shop.mult || 12)
  const chatOn = ref(false)

  const showLimit = ref(10);

  const list = computed(() => {
    return (props.byId[props.showKey] || []).slice(0, showLimit.value).filter(a => props.activeTypes[pt((props.byId.all || {})[a])])
  })

  const dist = computed(() => {
    const { distribution, distribution_ptc, useAptc } = props.shop || {};
    if (useAptc) return distribution_ptc || {};
    return distribution || {}
  })

  const all = computed(() => props.byId?.all || {})

  const keys = ['1000', '5000', '10000', '20000', '50000', '100000', '*']

  const ranks = computed(() => {
    const obj = {};
    for (let i = 0; i < keys.length; i++) {
      const bench = (dist.value || {})[keys[i]] || {};
      obj[keys[i]] = Object.keys(bench).sort((a, b) => bench[a] - bench[b])
    }
    return obj;
  })
  const lists = computed(() => {
    const obj = {};
    const arr = Object.keys(ranks.value || {});
    for (let i = 0; i < arr.length; i++) {
      obj[arr[i]] = ranks.value[arr[i]].filter(a => (props.activeTypes || {})[pt(all.value[a])])
    }
    return obj;
  })

  const cols = computed(() => {
    const ten_percent = Object.keys(props.byId || {}) > 30 ? '10%' : '3'
    return [
      {
        label: 'Plan',
        description: 'Name and provider for this plan'
      },
      {
        label: 'Premium',
        description: 'Plan premium for your household'
      },
      {
        label: 'OOP (avg)',
        description: 'Average out of pocket spend across all scenarios with this plan in place'
      },
      {
        label: 'Total (avg)',
        description: 'Average simulated total expense: premium + oop'
      },
      {
        label: 'Best Choice',
        description: 'Percentage of scenarios this plan is the best option'
      },
      {
        label: `Top ${ten_percent}`,
        description: `Percentage of scenarios this plan is in the top ${ten_percent} (lowest total spend)`
      },
      {
        label: `Last ${ten_percent}`,
        description: `Spend scenario where this plan is one of the worst ${ten_percent} (highest total spend)`
      }
    ]
  })

  const visible = ref({
    1: true,
    2: false,
    3: false,
    4: false,
    5: false,
    6: false
  })

  const scrollCount = ref(0);
  const setScroll = () => {
    if (scrollCount.value < 10) scrollCount.value++
    else {
      let allVisible = true;
      for (const k in visible.value) {
        if (!visible.value[k]) {
          const el = document.getElementById(`RC${k}`);
          if (el) {
            const rect = el.getBoundingClientRect();
            const topInView = rect.top < window.innerHeight - 50;
            const bottomNearTop = rect.bottom < window.innerHeight + 50;
            if (topInView || bottomNearTop) {
              visible.value[k] = true;
            } else {
              allVisible = false;
            }
          }
        }
      }
      if (allVisible) window.removeEventListener('scroll', setScroll)
    }
  }

  onMounted(() => {
    window.addEventListener('scroll', setScroll)
    setScroll()
  })

</script>

<style lang="scss" scoped>
  .__res_cat {
    width: 100%;
    padding-top: 10px;

    .__head {
      font-size: var(--text-lg);
      font-weight: 600;
      color: var(--ir-deep);
      text-align: center;
    }


    .__title {
      font-size: var(--text-xs);
      padding: 0 15px 20px 15px;
      text-align: center;
      color: var(--ir-deep);
      font-weight: 500;
    }

    .__list {
      max-height: 110vh;
      display: grid;
      grid-template-rows: auto 1fr;
      padding: 20px 0;
      width: 100%;
      overflow-x: scroll;

      > div {
        &:nth-child(2) {
          height: 100%;
          overflow-y: scroll;
        }
      }
    }

    .__policy_row {
      margin: 5px 0;
      background: var(--ir-bg2);
      border-radius: 0 8px 8px 0;
    }

    .__pil {
      width: 100%;
      display: grid;
      grid-template-columns: 25% 12fr 12fr 12fr 12fr 12fr 12fr;
      font-size: var(--text-xxs);
      font-weight: 600;
      color: var(--ir-off);
      align-items: end;

      > div {
        padding: 5px 10px;
        text-align: left;
        min-width: 80px;

        &:first-child {
          min-width: 130px;
        }
      }
    }
  }

  .__top3 {
    display: grid;
    grid-template-columns: 100%;
    grid-template-rows: auto auto;

    > div {
      &:first-child {
        padding: 20px;
        border-radius: 12px 12px 0 0;
        box-shadow: -2px 0 4px rgba(0, 0, 0, .1);
        background: var(--ir-bg1);
        //background: linear-gradient(135deg, var(--ir-a), var(--ir-s));
      }
    }
  }

  @media screen and (max-width: 1023px) {
    .__res_cat {
      .__head {
        padding-top: 80px;
      }

      .__title {
        padding-bottom: 10px;
      }
    }
  }

  .__ai_chip {
    margin-top: 50px;
    width: 100%;

    .AiChip {
      padding: 30px 1vw;
      border-radius: 15px;
      background: linear-gradient(130deg, var(--q-p10), var(--q-a10), var(--q-s10));
      box-shadow: 0 2px 8px var(--ir-light);
      width: 1500px;
      max-width: 100%;
      transition: all .3s;
      display: grid;
      grid-template-rows: 1fr auto;
    }

    .__on {
      width: 750px;
    }
  }
</style>
