<template>
  <div class="_fw">
    <div class="row justify-center">
      <div class="_cent pw2">
        <div class="row items-start">
          <div class="col-12 col-md-6 q-pa-md">
            <div class="__spd">
              <div class="__sld">
                <q-slider
                    track-size="40px"
                    :min="0"
                    :max="50000"
                    v-model="spd"
                    :style="{ zIndex: 5, opacity: 0}"
                ></q-slider>
              </div>
              <div class="__title">Annual Medical Bills</div>

              <div class="__graph">
                <div class="__mark" :style="{width}"></div>
                <div class="__l_h">
                  <!--              PLACEHOLDER-->
                  <div class="text-transparent text-xs">
                  </div>
                  <div class="__label alt-font" :style="{ left: width }">
                    <div>
                      <q-icon name="mdi-menu-up"></q-icon>
                    </div>
                    <div>{{ dollarString(spd, '$', 0) }}</div>
                  </div>
                </div>

              </div>

            </div>

            <div class="h40"></div>
            <q-separator v-if="$q.screen.lt.md" :dark="dark" class="q-my-sm"></q-separator>

          </div>
          <div class="col-12 col-md-6 q-pa-md">

            <div class="row justify-center">
              <div class="__cond">
                <div class="__title">Conditions/Events</div>

                <div>
                  <div>
                    <q-btn dense flat icon="mdi-menu-down" @click="incEvts(-1)"></q-btn>
                    <div class="__display">{{ evts }}</div>
                    <q-btn dense flat icon="mdi-menu-up" @click="incEvts(1)"></q-btn>
                  </div>
                  <div class="text-xxs tw-five text-ir-deep">Event count affects how deductibles/copays are applied
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>

    <div class="row q-px-sm">
      <div class="w500 mw100">
        <q-input :dark="dark" dense borderless class="_inp bg-ir-bg" v-model="searchInput"
                 placeholder="Search results">
          <template v-slot:prepend>
            <q-icon name="mdi-magnify"></q-icon>
          </template>
        </q-input>
      </div>
    </div>

    <div class="__grd">
      <div v-if="sorted.length">
        <div v-for="(plan, i) in !shop.useAptc ? sorted : sorted_ptc" :key="`plan-${i}`" class="__c cursor-pointer"
             @click="emit('update:selected', plan)" v-show="filtered.includes(plan._id)">
          <div class="_fa"
               :style="{background: 'var(--ir-bg)', borderBottom: `solid 3px var(--q-${productTypes[pt(plan)].color})`, borderTop: compare[plan._id || plan.id] ? 'solid 4px var(--q-accent)' : ''}">
            <spend-card
                :def_age="stats.age"
                :def_key="def_key"
                :useAptc="shop.useAptc"
                :mult="shop.mult"
                :model-value="plan"
                :scores="(shop.coverage_scores || {})[plan._id]"
                :dark="dark"
                :aptc="shop.aptc"
                :tax_rate="tax_rate"
            ></spend-card>
          </div>
          <div class="row justify-center q-py-xs">
            <div class="text-xs tw-six text-ir-mid alt-font">{{ ordinalSuffixOf(i + 1) }} <span
                class="text-xxs tw-four">of {{ sorted.length }}</span></div>
          </div>
        </div>
      </div>
      <div v-else>
        <div v-for="i in 5" :key="`sk-${i}`">
          <div class="__c bg-transparent">
            <q-skeleton class="h200 _fw"></q-skeleton>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
  import SpendCard from 'components/market/shop/cards/SpendCard.vue';

  import {computed, ref, watch} from 'vue';
  import {dollarString} from 'symbol-syntax-utils';
  import {ordinalSuffixOf} from 'components/market/utils';
  import {darkness} from 'src/utils/env/darkness';
  import {localCompareShop} from 'components/market/shop/utils/local-compare';
  import {productTypes, pt} from 'components/market/utils';


  const { dark } = darkness();
  const emit = defineEmits(['update:selected']);
  const props = defineProps({
    compare: {
      default: () => {
        return {}
      }
    },
    tax_rate: Number,
    enrollment: { required: false },
    all: {
      default: () => {
        return {}
      }
    },
    def_key: { default: 'family' },
    shop: {
      required: true, default: () => {
        return {}
      }
    }
  })

  const { sort, sorted, sorted_ptc, spd, evts, incEvts } = localCompareShop({
    aptc: computed(() => props.shop.aptc || 0),
    enrollment: computed(() => props.enrollment),
    coverages: computed(() => props.all),
    def_key: computed(() => props.def_key),
    def_age: computed(() => props.shop.stats?.age),
    tax_rate: computed(() => props.tax_rate)
  })
  const width = computed(() => Math.round(spd.value / 50000 * 100) + '%')

  const stats = computed(() => props.shop.stats)

  const searchInput = ref('');
  const filtered = computed(() => {
    const arr = [];
    const list = sorted.value;
    for (let i = 0; i < list.length; i++) {
      if (list[i].name.toLowerCase().includes(searchInput.value.toLowerCase())) arr.push(list[i]._id)
      else if ((list[i].carrierName || '').toLowerCase().includes(searchInput.value.toLowerCase())) arr.push(list[i]._id)
    }
    return arr;
  })

  const aptcWatch = computed(() => props.shop.useAptc);
  watch(() => aptcWatch, (nv, ov) => {
    if (nv !== ov) {
      sort()
    }
  }, { immediate: true });
  const spdSet = ref(false);
  watch(() => props.all, (nv, ov) => {
    if (nv && Object.keys(nv).length !== Object.keys(ov || {}).length) sort()
    if (props.shop.spend && !spdSet.value) {
      spd.value = props.shop.spend;
      spdSet.value = true;
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>

  .__title {
    color: var(--ir-deep);
    font-weight: 600;
    font-size: 1rem;
    text-align: center;
    padding: 10px 5px 5px 5px;
  }

  .__spd {
    width: 100%;
    position: relative;

    .__sld {
      position: absolute;
      top: 50%;
      left: 0;
      transform: translate(0, -50%);
      width: 100%;
      z-index: 5;
    }

    .__graph {
      pointer-events: none;
      width: 100%;
      border-radius: 5px;
      position: relative;
      background: var(--ir-bg2);

      .__mark {
        border-radius: inherit;
        z-index: 1;
        pointer-events: none;
        position: absolute;
        left: 0;
        right: 0;
        height: 100%;
        bottom: 0;
        width: 0;
        transition: height .1s;
        background: linear-gradient(270deg, var(--q-a4), var(--q-a3));
      }

      .__l_h {
        width: 100%;
        position: relative;
        height: 2ch;
        padding: 5px 0;

        .__label {
          line-height: .7rem;
          position: absolute;
          top: 100%;
          transform: translate(-50%, -2px);
          font-size: var(--text-xs);
          font-weight: bold;
          color: var(--q-accent);
          text-align: center;
        }
      }
    }
  }

  .__grd {
    width: 100%;
    overflow-x: scroll;
    padding: 20px 10px;

    > div {
      display: grid;
      grid-template-columns: repeat(100, min(85vw, 380px));
      grid-template-rows: repeat(1, auto);
      gap: 10px;

      .__c {
        display: grid;
        grid-template-columns: 100%;
        grid-template-rows: 1fr auto;

        > div {
          &:first-child {
            padding: 20px;
            border-radius: 12px 12px 0 0;
            box-shadow: -2px 0 4px rgba(0, 0, 0, .1);
            background: var(--ir-bg1);
            //background: linear-gradient(135deg, var(--ir-a), var(--ir-s));
          }
        }
      }
    }
  }

  .__cond {
    padding: 0 0 20px 0;
    cursor: pointer;

    > div {
      &:nth-child(2) {
        display: grid;
        grid-template-columns: auto auto;
        align-items: center;

        > div {
          padding: 3px 8px;

          &:first-child {
            display: flex;
            align-items: center;

            .__display {
              padding: 2px 16px;
              border-radius: 5px;
              background: var(--ir-bg2);
              font-size: var(--text-xs);
              font-weight: 600;
            }
          }

        }
      }
    }
  }


</style>
