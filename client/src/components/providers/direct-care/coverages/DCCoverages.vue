<template>
  <div class="_fw">
    <div class="q-pa-sm">
      <div class="tw-six font-1r flex items-center">
        <div>Your Panels</div>
        <q-btn @click="adding = true" dense flat icon="mdi-plus" color="primary"></q-btn>
      </div>
      <div class="font-7-8r">You create panels - which get filled with memberships. They are like a product. Create
        public panels or private panels for employer or other groups
      </div>
    </div>

    <q-tab-panels class="_panel" :model-value="!!viewing" animated transition-next="fade-out" transition-prev="fade-in">
      <q-tab-panel class="_panel" :name="false">
        <div class="q-py-sm _fw mw500">
          <q-input dense filled v-model="search.text">
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
        </div>
        <div class="row q-py-sm">
          <div v-for="(c, i) in c$.data" :key="`c-${i}`" class="col-12 col-md-4 q-pa-sm">
            <div class="__c _hov">
              <q-btn dense flat icon="mdi-dots-vertical" class="t-r-a">
                <q-menu>
                  <div class="w300 mw100">
                    <q-list separator>
                      <q-item clickable @click="editing = c">
                        <q-item-section>
                          <q-item-label>Edit</q-item-label>
                        </q-item-section>
                        <q-item-section side>
                          <q-icon name="mdi-pencil-box"></q-icon>
                        </q-item-section>
                      </q-item>
                      <q-item clickable @click="viewing = c">
                        <q-item-section>
                          <q-item-label>View</q-item-label>
                        </q-item-section>
                        <q-item-section side>
                          <q-icon name="mdi-eye"></q-icon>
                        </q-item-section>
                      </q-item>
                    </q-list>
                  </div>
                </q-menu>
              </q-btn>
              <coverage-card :model-value="c"></coverage-card>
            </div>
          </div>
        </div>

        <pagination-row v-bind="{ pageRecordCount, pagination, limit, h$:c$ }"></pagination-row>
      </q-tab-panel>
      <q-tab-panel class="_panel" :name="true">

        <div class="row justify-center">
          <div class="_sent">
            <div class="row justify-end q-py-md">
              <q-btn dense flat color="red" icon="mdi-close" @click="viewing = undefined"></q-btn>
            </div>
            <coverage-page :model-value="viewing"></coverage-page>
          </div>
        </div>
      </q-tab-panel>
    </q-tab-panels>


    <common-dialog setting="right" :model-value="!!editing || adding" @update:model-value="toggleDialog">
      <div class="_fw q-pa-md">
        <dc-coverage-form :model-value="editing" @update:model-value="toggleDialog(false)" :provider-id="providerId"></dc-coverage-form>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import DcCoverageForm from 'components/providers/direct-care/coverages/DcCoverageForm.vue';
  import CoverageCard from 'components/coverages/cards/CoverageCard.vue';
  import PaginationRow from 'components/utils/pagination/PaginationRow.vue';
  import CoveragePage from 'components/coverages/pages/CoveragePage.vue';

  import {computed, ref} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {HFind} from 'src/utils/hFind';
  import {fakeId} from 'src/utils/global-methods';
  import {useCoverages} from 'stores/coverages';
  import {HQuery} from 'src/utils/hQuery';

  const coverageStore = useCoverages();

  const providerId = computed(() => LocalStorage.getItem('provider_id'))

  const editing = ref();
  const adding = ref(false);
  const viewing = ref(false);

  const toggleDialog = (val) => {
    if (!val) {
      editing.value = '';
      adding.value = false;
    }
  }

  const { search, searchQ } = HQuery({})
  const limit = ref(10);
  const { h$:c$, pageRecordCount, pagination } = HFind({
    store: coverageStore,
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          provider: providerId.value || fakeId
        }
      }
    })
  })

</script>

<style lang="scss" scoped>

  .__c {
    position: relative;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 4px var(--ir-light);
  }
</style>
