<template>
  <div>
    <div class="q-pa-md tw-six font-1r">{{ form._id ? 'Edit' : 'Create' }} Panel</div>
    <div class="_f_l _f_chip">Info</div>
    <div class="_form_grid _f_g_r">
      <!--      NAME-->
      <div class="_form_label">Name</div>
      <div class="q-pa-sm">
        <q-input dense v-model="form.name" @update:model-value="autoSave('name')"
                 placeholder="Name panel/membership..."></q-input>
      </div>
      <!--      DESCRIPTION-->
      <div class="_form_label">Description</div>
      <div class="q-pa-sm">
        <q-input
            dense
            autogrow
            placeholder="Short description..."
            maxlength="200"
            counter
            v-model="form.description"
            @blur="autoSave('description', form.description)"
        ></q-input>
      </div>
      <div class="_form_label">Webpage</div>
      <div class="q-pa-sm">
        <q-input dense v-model="form.webpage" @update:model-value="autoSave('webpage')"></q-input>

      </div>
      <div class="_form_label">Type</div>
      <div class="q-pa-sm">
        <q-radio v-model="form.dpc" :val="true" label="Direct Primary Care"></q-radio>
        <q-radio v-model="form.dpc" :val="false" label="Direct Specialty Care"></q-radio>
      </div>
    </div>
    <!--      COVERED-->
    <div class="_f_l _f_chip">Access</div>
    <div class="_form_grid _f_g_r">
      <div class="_form_label">Covered</div>
      <div class="q-pa-sm">
        <div class="font-3-4r">Specify whether this is a contract for individuals or for groups. "Group" doesn't mean
          "for individuals who are part of a group." It means the group is who your agreement is with.
        </div>
        <q-radio v-model="form.covered" label="Group" val="group"
                 @update:model-value="autoSave('covered', $event)"></q-radio>
        <q-radio v-model="form.covered" label="Individual" val="individual"
                 @update:model-value="autoSave('covered', $event)"></q-radio>
      </div>
      <div class="_form_label">Public Offering</div>
      <div class="q-pa-sm">
        <q-checkbox :model-value="!!form.public" @update:modelValue="form.public = $event; autoSave('public', $event)" label="Publicly Available"></q-checkbox>
      </div>


    </div>
    <div class="_f_l _f_chip">Membership Fee</div>

    <!--      PREMIUM-->
    <div class="_fw">
      <q-expansion-item group="0">
        <template v-slot:header>
          <premium-item :model-value="form"></premium-item>
        </template>

        <q-tab-panels
            class="_panel"
            animated
            v-model="addingRate"
            transition-next="jump-up"
            transition-prev="jump-down">
          <q-tab-panel class="_panel" :name="false">
            <div class="_fw _oxh">
              <premium-form v-model="form.premium" @update:model-value="autoSave('premium', $event)">
                <template v-slot:rate-bottom>
                  <div class="_form_label">
                    Location Based Rates
                  </div>
                  <div class="q-pa-sm">
                    <q-chip v-if="form._id" color="transparent" clickable @click="addingRate = true">
                      <span
                          class="q-mr-sm">Location-Based Rates ({{
                          $possiblyPlural('Added', form.rates, '', '')
                        }})</span>
                      <q-icon color="red" name="mdi-map-marker"></q-icon>
                    </q-chip>
                    <div class="text-italic q-pa-sm" v-else>Finish creating coverage, then you can add location based
                      rates
                    </div>
                  </div>
                </template>
              </premium-form>
            </div>
          </q-tab-panel>
          <q-tab-panel class="_panel" :name="true">
            <geo-rates :coverage="form"></geo-rates>
          </q-tab-panel>
        </q-tab-panels>

      </q-expansion-item>
    </div>
    <div class="_f_l _f_chip">Standard Deductible</div>
    <div class="q-pa-sm font-3-4r">Yes, Direct Care memberships may utilize a deductible structure - especially if
      trying to provide a group plan with a MEC plan that complies with HDHP metrics
    </div>
    <div class="_form_grid _f_g_r">
      <div class="_form_label">Preventive</div>
      <div class="row q-pa-sm">
        <q-checkbox
            v-model="form.preventive"
            label="Preventive services covered - no deductible"
            @update:model-value="autoSave('preventive', $event)"
        ></q-checkbox>
      </div>
      <deductible-form v-model="form.deductible" @update:model-value="autoSave('deductible', $event)"></deductible-form>

    </div>


    <template v-if="form._id">
      <div class="_f_l _f_chip">Special Deductibles</div>
      <deductibles-form path="deductibles" v-model="form.deductibles" :coverage-id="form._id"></deductibles-form>

      <div class="_f_l _f_chip">Special Copays</div>
      <deductibles-form path="copays" v-model="form.copays" :coverage-id="form._id"></deductibles-form>
    </template>

    <div class="_f_l _f_chip">Other Settings</div>
    <div class="q-pa-sm">
      <div class="_form_grid">
        <div class="_form_label">Max Age</div>
        <div class="q-pa-sm">
          <money-input prefix="" v-model="form.maxAge" @blur="autoSave('maxAge',form.maxAge)"></money-input>
        </div>
      </div>
    </div>

    <div class="_f_l _f_chip">Service Area</div>
    <div class="q-pa-sm">
      <coverage-boundary adding :coverage="form"></coverage-boundary>
    </div>

    <div class="_f_l _f_chip">Documents</div>
    <div class="q-pa-sm">
      <multi-file-uploader
          @remove="removeDoc"
          v-model="form.documents"
          @update:model-value="autoSave('documents', $event)"
      ></multi-file-uploader>
    </div>

    <div class="_f_l _f_chip">Video</div>
    <div class="q-pa-sm">
      <video-form allow-url @remove="removeVideo" v-model="form.video"
                  @update:model-value="autoSave('video', $event)"></video-form>
    </div>

    <div class="row justify-end q-py-lg">
      <q-btn
          class="_p_btn"
          no-caps
          :label="`Save ${form?._id ? 'Changes' : ''}`"
          :icon-right="!form?._id ? 'mdi-rocket-launch' : 'mdi-content-save'"
          @click="manualSave"
      ></q-btn>
    </div>
  </div>
</template>

<script setup>
  import PremiumItem from 'components/plans/cards/PremiumItem.vue';
  import GeoRates from 'components/coverages/rates/forms/GeoRates.vue';
  import PremiumForm from 'components/coverages/forms/PremiumForm.vue';
  import DeductibleForm from 'components/coverages/forms/DeductibleForm.vue';
  import DeductiblesForm from 'components/coverages/forms/DeductiblesForm.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import CoverageBoundary from 'components/coverages/forms/CoverageBoundary.vue';
  import MultiFileUploader from 'components/common/uploads/components/MultiFileUploader.vue';
  import VideoForm from 'components/common/uploads/video/VideoForm.vue';

  import {HForm, HSave} from 'src/utils/hForm';
  import {useCoverages} from 'stores/coverages';
  import {computed, ref, watch} from 'vue';
  import {$possiblyPlural} from 'src/utils/global-methods';

  const coverageStore = useCoverages();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: false },
    providerId: { required: true }
  })

  const addingRate = ref(false);


  const formFn = (defs) => {
    return {
      provider: props.providerId,
      type: 'dc',
      covered: 'individual',
      ...defs
    }
  }
  const manuallySaved = ref(false);
  const dirty = ref(false);
  const { form, save } = HForm({
    store: coverageStore,
    formFn,
    validate: true,
    vOpts: { 'provider': { name: 'Provider', v: ['notEmpty'] }, 'name': { name: 'Name', v: ['notEmpty'] } },
    value: computed(() => props.modelValue),
    beforeFn: (item) => {
      item.type = 'dc'
      if (!item.provider) item.provider = props.providerId
      if (item.covered === 'individual') item.shop = true
      return item;
    },
    afterFn: (val) => {
      if (manuallySaved.value) emit('update:model-value', val)
      manuallySaved.value = false
    },
  })

  const { autoSave, patchObj } = HSave({
    form,
    save,
    onChange: () => dirty.value = true,
    store: coverageStore,
    pause: ref(true),
  })

  watch(() => props.providerId, (nv) => {
    if (nv && !form.value.provider) form.value.provider = nv;
  }, { immediate: true })


  const removeDoc = (file, i) => {
    form.value.documents.splice(i, 1);
    autoSave('documents');
  }

  const removeVideo = () => {
    if (form.value._id) coverageStore.patch(form.value._id, { $unset: { video: '' } })
    delete form.value.video;
  }

  const params = { special_change: [] }

  const manualSave = async () => {
    manuallySaved.value = true;
    if (!form.value._id) save();
    else {
      const patched = await coverageStore.patch(form.value._id, patchObj.value, params.value)
      if (patched._id) {
        patchObj.value = {}
        emit('update:model-value', patched)
        manuallySaved.value = false;
      }
    }
  }

</script>

<style lang="scss" scoped>

</style>
